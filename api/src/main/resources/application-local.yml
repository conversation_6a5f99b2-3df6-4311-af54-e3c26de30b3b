# Server Configuration
server:
  port: 8084
logging:
  level:
    root: INFO  # Default log level
    org:
      springframework:
        web: ERROR
        boot: ERROR
        security: DEBUG  # If using Spring Security
    jo.capitalbank.ms: DEBUG

# CBOJ Custom Properties
# OAuth2 Security Configuration
cboj:
  security:
    oauth2:
      enabled: false

spring:
  # Database Configuration
  datasource:
    url: *******************************************************************************************************
    username: sa
    password: mY_9pAssw0rd1
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
  # Flyway Configuration
  flyway:
    validate-on-migrate: false
#  security:
#    oauth2:
#      resourceserver:
#        jwt:
#          issuer-uri: https://sso-stg-cboj.capitalbank.jo:8443/auth/realms/cboj-mai


http-lib:
  tls:
    keystore:
      path: sdfds
      password: dfsd


