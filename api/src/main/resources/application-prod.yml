# Server Configuration
server:
  port: 8080

# CBOJ Custom Properties
# OAuth2 Security Configuration
cboj:
  security:
    oauth2:
      enabled: true

spring:
  # Database Configuration
  datasource:
    url: *******************************************************************************************************
    username: sa
    password: mY_9pAssw0rd1
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso-stg-cboj.capitalbank.jo:8443/auth/realms/cboj-mai


