package jo.capitalbank.ms.models.responses;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculationBackendResponse {

    private CalculationBackendBody body;
    private String statusCode;
    private Object headers;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CalculationBackendBody {
        private long errorCode;
        private String descriptionCode;
        private BigDecimal amount;
        private long points;
        private String identifierValue;

    }
}
