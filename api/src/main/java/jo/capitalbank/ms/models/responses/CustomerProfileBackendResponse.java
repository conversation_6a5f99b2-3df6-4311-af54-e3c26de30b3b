package jo.capitalbank.ms.models.responses;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerProfileBackendResponse {
    private CustomerBodyResponse body;
    private String statusCode;
    private Object headers;


    @Builder
    @Data
    @AllArgsConstructor
    public static class CustomerBodyResponse {
        private int errorCode;
        private String descriptionCode;
        private String identifierType;
        private String identifierValue;
        private String currentPoints;
        private String currentPointsRJ;
        private String rjMsg;
        private String rjRequestUrl;
        private String currentPointsValue;
        private String accumulatedCashback;
        private String accumulatedPoints;
        private String expiryPoint;
        private int status;
        private String productIdFontColor;
        private String productId;
        private String productTitle;
        private String productDescription;
        private boolean showRj;
        private boolean showSteps;
        private boolean gamificationShow;
        private String gamificationIcon;
        private String gamificationUrl;
    }
}