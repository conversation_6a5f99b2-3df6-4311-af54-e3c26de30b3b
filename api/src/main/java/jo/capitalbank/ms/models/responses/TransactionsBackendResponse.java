package jo.capitalbank.ms.models.responses;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionsBackendResponse {
    private Object headers;
    private String statusCode;
    private TransactionsBackendBody body;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TransactionsBackendBody {
        private int errorCode;
        private String descriptionCode;
        private TransactionBody[] data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TransactionBody {
        private String title;
        private String type;
        private Items[] items;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Items {
        private String title;
        private long date;
        private long points;
        private String type;
    }

}
