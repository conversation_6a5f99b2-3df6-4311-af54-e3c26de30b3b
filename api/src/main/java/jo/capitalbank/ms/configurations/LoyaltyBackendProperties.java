package jo.capitalbank.ms.configurations;

import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.Paths;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@ConfigurationProperties(prefix = "loyalty-backend.headers")
@Configuration
@Data
public class LoyaltyBackendProperties {

    private String acceptLanguage;
    private String androidOsVersion;
    private String authorization;
    private String sdkVersion;

    @Bean
    public HttpHeaders headers() {
        log.info("Using LoyaltyBackend headers ----> acceptLanguage={},authorization={}", acceptLanguage, authorization);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept-Language", acceptLanguage);
        headers.set("Authorization", authorization);
        headers.set("android_os_version", androidOsVersion);
        headers.set("sdk_version", sdkVersion);

        return headers;
    }

}
