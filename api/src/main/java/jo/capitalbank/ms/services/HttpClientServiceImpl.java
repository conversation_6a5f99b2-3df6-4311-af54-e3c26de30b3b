package jo.capitalbank.ms.services;

import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.LoyaltySystemException;
import jo.capitalbank.ms.library.common.services.HttpClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

@Slf4j
@Service
public class HttpClientServiceImpl extends HttpClientService {
    @Override
    protected ResponseEntity<?> handleConnectionException(String url, ResourceAccessException ex) {
        log.error("Timeout or Error connecting to {}", url, ex);
        throw new LoyaltySystemException("Timeout or Error connecting to " + url, ErrorCodes.LOYALTY_SYSTEM_ERROR, null, null);
    }
}
