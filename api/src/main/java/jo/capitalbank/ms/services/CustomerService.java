package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.dto.CustomerProfileResponse;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.error.code.HttpStatusCodes;
import jo.capitalbank.ms.library.error.exception.OBBusinessException;
import jo.capitalbank.ms.library.error.exception.SubException;
import jo.capitalbank.ms.mapper.CustomerProfileMapper;
import jo.capitalbank.ms.mapper.StepsInfoMapper;
import jo.capitalbank.ms.mapper.TransactionResponseMapper;
import jo.capitalbank.ms.models.responses.CustomerProfileBackendResponse;
import jo.capitalbank.ms.models.responses.StepInfoBackendResponse;
import jo.capitalbank.ms.models.responses.TransactionsBackendResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class CustomerService {
    @Autowired
    HttpClientServiceImpl httpClientServiceImpl;
    @Autowired
    HttpHeaders headers;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerProfileMapper customerProfileMapper;
    @Autowired
    private StepsInfoMapper stepsInfoMapper;

    @Autowired
    private TransactionResponseMapper transactionResponseMapper;

    @Value("${loyalty-backend.host}")
    private String LOYALTY_BACKEND_HOST;

    @Value("${loyalty-backend.port}")
    private String PORT;

    private static final String LOYALTY_CUSTOMER_ENDPOINT = "/api/v1.3/customer/";
    private static final String LOYALTY_STEPINFO_ENDPOINT = "/api/v1.3/steps_info/";
    private static final String LOYALTY_TRX_ENDPOINT = "/api/v1.3/trx/";
    private static final String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    public ResponseEntity<?> getCustomerDetails(Long id) {


        ResponseEntity<?> response = httpClientServiceImpl.sendRequest(LOYALTY_BACKEND_HOST +PORT + LOYALTY_CUSTOMER_ENDPOINT + id
                , HttpMethod.GET
                , headers
                , new HashMap<>()
                , null);

        log.info("after calling loyalty backend ----> response={}", response.getBody());

        CustomerProfileBackendResponse backendResponse = objectMapper.convertValue(response.getBody(), CustomerProfileBackendResponse.class);


        if (backendResponse.getBody().getErrorCode() == 0)
            return customerProfileDtoMapper(backendResponse.getBody(), response);
        else {
            List<SubException> subExceptions = new ArrayList<>();
            subExceptions.add(new SubException(null, backendResponse.getBody().getDescriptionCode(), String.valueOf(backendResponse.getBody().getErrorCode())));
            throw new OBBusinessException("Customer Not Found", ErrorCodes.LOYALTY_SYSTEM_ERROR) {
                @Override
                public HttpStatusCodes getHttpStatusCode() {
                    return super.getHttpStatusCode();
                }

                @Override
                public Enum<?> getErrorCode() {
                    return super.getErrorCode();
                }
            };
        }
    }

    public ResponseEntity<?> userStepInfoDetails(Long id) {
        var reponse = httpClientServiceImpl.sendRequest(LOYALTY_BACKEND_HOST+PORT + LOYALTY_STEPINFO_ENDPOINT + id,
                HttpMethod.GET,
                headers,
                new HashMap<>(),
                null);
        log.info("after calling loyalty -->response={}", reponse.getBody());
        var backendReponse = objectMapper.convertValue(reponse.getBody(), StepInfoBackendResponse.class);
        log.info("loyalty backend -->response={}", backendReponse);

        if (backendReponse.getBody().getErrorCode() == 0)
            return stepInfoDtoMapper(backendReponse.getBody(), reponse);
        else {
            List<SubException> subExceptions = new ArrayList<>();
            subExceptions.add(new SubException(null, backendReponse.getBody().getDescriptionCode(), String.valueOf(backendReponse.getBody().getErrorCode())));
            throw new OBBusinessException("Customer Not Found", ErrorCodes.LOYALTY_SYSTEM_ERROR) {
                @Override
                public HttpStatusCodes getHttpStatusCode() {
                    return super.getHttpStatusCode();
                }

                @Override
                public Enum<?> getErrorCode() {
                    return super.getErrorCode();
                }
            };
        }

    }

    public ResponseEntity<?> getCustomerLoyaltyTransactions(Long id, String pageSize, String pageNumber, String sort, String type) {
        var queryParams = new HashMap<String, String>();
        queryParams.put("pageSize", pageSize);
        queryParams.put("pageNumber", pageNumber);
        queryParams.put("sort", sort);
        queryParams.put("type", type);
        var response = httpClientServiceImpl.sendRequest(LOYALTY_BACKEND_HOST +PORT + LOYALTY_TRX_ENDPOINT + id, HttpMethod.GET, headers, queryParams, null);
        log.info("after calling loyalty backend -->response={}", response.getBody());

        var transactionsBackendResponse = objectMapper.convertValue(response.getBody(), TransactionsBackendResponse.class);


        //return new ResponseEntity<>(transactionsBackendResponse.getBody(), HttpStatus.OK);
       return transactionDtoMapper(transactionsBackendResponse.getBody(), response);
    }

    private ResponseEntity<?> transactionDtoMapper(TransactionsBackendResponse.TransactionsBackendBody source, ResponseEntity<?> responseEntity) {
       var transactionResponse = transactionResponseMapper.mapTransactionResponse(source);
        var status = new CommonResponse.Status(
                responseEntity.getStatusCode().is2xxSuccessful(),
                String.valueOf(responseEntity.getStatusCode()),
                SUCCESS_MESSAGE,
                null,
                null,
                null
        );

        var commonResponse = CommonResponse.builder().status(status).response(transactionResponse).build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }


    private ResponseEntity<?> stepInfoDtoMapper(StepInfoBackendResponse.StepInfoBody response, ResponseEntity<?> responseEntity) {

        var stepsInfoResponse = stepsInfoMapper.mapStepsInfoResponse(response);
        log.info("after mapping -->response={}", stepsInfoResponse);
        var status = new CommonResponse.Status(
                responseEntity.getStatusCode().is2xxSuccessful(),
                String.valueOf(responseEntity.getStatusCode()),
                SUCCESS_MESSAGE,
                null,
                null,
                null
        );
        var commonResponse = CommonResponse.builder().status(status).response(stepsInfoResponse).build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);

    }

    private ResponseEntity<?> customerProfileDtoMapper(
            CustomerProfileBackendResponse.CustomerBodyResponse customerProfile,
            ResponseEntity<?> responseEntity) {

        CommonResponse.Status status = new CommonResponse.Status(
                responseEntity.getStatusCode().is2xxSuccessful(),
                String.valueOf(responseEntity.getStatusCode().value()),
                SUCCESS_MESSAGE,
                null,
                null,
                null
        );

        CustomerProfileResponse customerProfileDto = customerProfileMapper.mapToDto(customerProfile);
        CommonResponse commonResponse =
                CommonResponse.builder()
                        .status(status)
                        .response(customerProfileDto)
                        .build();

        return new ResponseEntity<>(commonResponse, responseEntity.getHeaders(), HttpStatus.OK);
    }
}
