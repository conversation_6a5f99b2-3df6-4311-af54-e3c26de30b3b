package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.LoyaltySystemException;
import jo.capitalbank.ms.library.common.dto.CommonResponse;


import jo.capitalbank.ms.library.error.exception.SubException;
import jo.capitalbank.ms.mapper.CalculationAmountMapper;
import jo.capitalbank.ms.models.requests.CalculationRequest;
import jo.capitalbank.ms.models.requests.RedeemRequest;
import jo.capitalbank.ms.models.responses.CalculationBackendResponse;
import jo.capitalbank.ms.models.responses.LoyaltyGainBackendResponse;
import jo.capitalbank.ms.models.requests.LoyaltyGainRequest;
import jo.capitalbank.ms.models.responses.RedeemBackendResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class PointsService {

    @Autowired
    HttpClientServiceImpl httpClientServiceImpl;
    @Autowired
    HttpHeaders headers;
    @Autowired
    private ObjectMapper objectMapper;


    @Value("${loyalty-backend.host}")
    private String LOYALTY_BACKEND_HOST;
    @Value("${loyalty-backend.port}")
    private String PORT;

    private static final String LOYALTY_GAIN_ENDPOINT = "/api/v1.3/customer/gain/";
    private static final String LOYALTY_REDEEM_POINT_ENDPOINT = "/api/v1.3//customer/redeem/";
    private static final String LOYALTY_CALCULATE_ENDPOINT = "/api/v1.3/customer/calculate_amount/";
    private static final String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    @Autowired
    private CalculationAmountMapper calculationAmountMapper;


    public ResponseEntity<?> gainLoyaltyRequest(LoyaltyGainRequest request) {
        var response = httpClientServiceImpl.sendRequest(LOYALTY_BACKEND_HOST +PORT+ LOYALTY_GAIN_ENDPOINT + request.getIdentifierValue(), HttpMethod.POST
                , headers, new HashMap<>(), request);
        log.info("after calling loyalty backend ----> response={}", response.getBody());
        var loyaltyGainBackendResponse = objectMapper.convertValue(response.getBody(), LoyaltyGainBackendResponse.class);
        log.debug("loyaltyGainBackendResponse={}", loyaltyGainBackendResponse);

        if (loyaltyGainBackendResponse.getBody().getErrorCode() == 0)
            return gainLoyaltyDtoMapper(loyaltyGainBackendResponse, response);
        else {
            List<SubException> subExceptions = new ArrayList<>();
            subExceptions.add(new SubException(String.valueOf(loyaltyGainBackendResponse.getBody().getErrorCode()),
                    loyaltyGainBackendResponse.getBody().getDescriptionCode(),
                    String.valueOf(loyaltyGainBackendResponse.getBody().getErrorCode())));

            throw new LoyaltySystemException(loyaltyGainBackendResponse.getBody().getDescriptionCode(),
                    ErrorCodes.LOYALTY_SYSTEM_ERROR, subExceptions, new HashMap<>());
        }


    }

    public ResponseEntity<?> redeemPoints(RedeemRequest request) {
        var response = httpClientServiceImpl.sendRequest(LOYALTY_BACKEND_HOST +PORT+ LOYALTY_REDEEM_POINT_ENDPOINT + request.getIdentifierValue(), HttpMethod.POST
                , headers, new HashMap<>(), request);

        log.info("after calling loyalty backend ----> response={}", response.getBody());
        var redeemBackendResponse = objectMapper.convertValue(response.getBody(), RedeemBackendResponse.class);
        log.info("backendResponse={}", redeemBackendResponse);

        if (redeemBackendResponse.getBody().getErrorCode() == 0)
            return redeemPointsDtoMapper(redeemBackendResponse, response);
        else {
            List<SubException> subExceptions = new ArrayList<>();
            subExceptions.add(new SubException(String.valueOf(redeemBackendResponse.getBody().getErrorCode()),
                    redeemBackendResponse.getBody().getDescriptionCode(),
                    String.valueOf(redeemBackendResponse.getBody().getErrorCode())));

            throw new LoyaltySystemException(redeemBackendResponse.getBody().getDescriptionCode(),
                    ErrorCodes.LOYALTY_SYSTEM_ERROR, subExceptions, new HashMap<>());

        }

    }

    public ResponseEntity<?> calculatePointsAmount(CalculationRequest request) {
        var response = httpClientServiceImpl.sendRequest(LOYALTY_BACKEND_HOST +PORT+ LOYALTY_CALCULATE_ENDPOINT + request.getIdentifierValue(),
                HttpMethod.POST,
                headers,
                new HashMap<>(),
                request);
        log.info("after calling loyalty backend ----> response={}", response.getBody());
        var calculationBackendResponse = objectMapper.convertValue(response.getBody(), CalculationBackendResponse.class);
        log.info("backendResponse={}", calculationBackendResponse);

        if (calculationBackendResponse.getBody().getErrorCode() == 0)
            return calculationDtoMapper(calculationBackendResponse.getBody(), response);
        else {
            List<SubException> subExceptions = new ArrayList<>();
            subExceptions.add(new SubException(String.valueOf(calculationBackendResponse.getBody().getErrorCode()),
                    calculationBackendResponse.getBody().getDescriptionCode(),
                    String.valueOf(calculationBackendResponse.getBody().getErrorCode())));

            throw new LoyaltySystemException(calculationBackendResponse.getBody().getDescriptionCode(),
                    ErrorCodes.LOYALTY_SYSTEM_ERROR, subExceptions, new HashMap<>());

        }
    }

    private ResponseEntity<?> calculationDtoMapper(CalculationBackendResponse.CalculationBackendBody backendResponse, ResponseEntity<?> response) {

        var status = new CommonResponse.Status(true,
                String.valueOf(response.getStatusCode()),
                SUCCESS_MESSAGE,
                null,
                null,
                null);
        var calculationAmountResponse = calculationAmountMapper.mapToDto(backendResponse);
        var commonResponse = CommonResponse.builder().status(status).response(calculationAmountResponse).build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }

    private ResponseEntity<?> redeemPointsDtoMapper(RedeemBackendResponse backendResponse, ResponseEntity<?> responseEntity) {
        var status = new CommonResponse.Status(responseEntity.getStatusCode().is2xxSuccessful(),
                String.valueOf(responseEntity.getStatusCode()),
                SUCCESS_MESSAGE,
                null,
                null,
                null);
        var response = CommonResponse.builder().status(status).build();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private ResponseEntity<?> gainLoyaltyDtoMapper(LoyaltyGainBackendResponse backendResponse, ResponseEntity<?> responseEntity) {

        var status = new CommonResponse.Status(responseEntity.getStatusCode().is2xxSuccessful(),
                String.valueOf(responseEntity.getStatusCode()),
                SUCCESS_MESSAGE,
                null,
                null,
                null);
        var response = CommonResponse.builder()
                .status(status)
                .build();

        return new ResponseEntity<>(response, HttpStatus.OK);

    }


}
