package jo.capitalbank.ms.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionsResponse {


    private Transaction[] transactions;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Transaction {
        private String title;
        private String type;
        private TransactionItem[] transaction;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TransactionItem {
        private String title;
        private long date;
        private long points;
        private String type;

    }

}
