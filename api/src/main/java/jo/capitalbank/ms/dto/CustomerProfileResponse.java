package jo.capitalbank.ms.dto;


import io.swagger.v3.oas.models.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerProfileResponse extends Schema {

    private String customerId;
    private String currentPoints;
    private String currentPointsRJ;
    private String rjMessage;
    private String rjRequestUrl;
    private String currentPointsValue;
    private String accumulatedCashback;
    private String accumulatedPoints;
    private String expiryPoint;
    private int status;
}
