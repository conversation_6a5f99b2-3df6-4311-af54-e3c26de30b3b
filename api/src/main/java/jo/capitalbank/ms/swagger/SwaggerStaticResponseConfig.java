package jo.capitalbank.ms.swagger;

import io.swagger.v3.oas.models.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class SwaggerStaticResponseConfig {

    private final CustomerProfileResponseExample customerProfileResponseExample;



    public SwaggerStaticResponseConfig(CustomerProfileResponseExample customerProfileResponseExample) {
        this.customerProfileResponseExample = customerProfileResponseExample;
    }

    @Bean
    public OpenApiCustomizer customStaticResponseCustomizer() {
        return openApi -> {
            if (openApi.getPaths().containsKey("/v1/customers/{id}")) {
                log.info("customer details response API response -->path");
                var operation = openApi.getPaths().get("/v1/customers/{id}").getGet();
                ApiResponses responses = operation.getResponses();
                responses.addApiResponse("200", customerProfileResponseExample.getStaticCustomerProfileResponse200());
                responses.addApiResponse("200-customerNotFound", customerProfileResponseExample.customerNotFoundExample());
            }

        };
    }
}
