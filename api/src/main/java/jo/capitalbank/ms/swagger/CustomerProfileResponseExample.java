package jo.capitalbank.ms.swagger;

import io.swagger.v3.oas.models.media.*;
import io.swagger.v3.oas.models.responses.ApiResponse;
import org.springframework.stereotype.Component;

@Component
public class CustomerProfileResponseExample {


    public ApiResponse getStaticCustomerProfileResponse200() {

        Schema<?> statusSchema = new ObjectSchema()
                .addProperty("success", new BooleanSchema().example(true))
                .addProperty("code", new StringSchema().example("200"))
                .addProperty("reasonCode", new StringSchema().example("The Operation has been Successfully Completed"))
                .addProperty("backendError", new StringSchema().example(""))
                .addProperty("backendCode", new StringSchema().example(""));


        Schema<?> responseSchema = new ObjectSchema()
                .addProperty("customerId", new StringSchema().example("2057612"))
                .addProperty("currentPoints", new StringSchema().example("60.00"))
                .addProperty("currentPointsRJ", new StringSchema().example("0.00"))
                .addProperty("rjMessage", new StringSchema().example("Earn Miles as you shop with Visa RJ Debit Card \n Enjoy Royal Club benefits & 12 Free lounge entries"))
                .addProperty("rjRequestUrl", new StringSchema().example("https://loyalty-test.capitalbank.jo:8443/api/v1/rj/webview/open/2057612?lang=en"))
                .addProperty("currentPointsValue", new StringSchema().example("0.300"))
                .addProperty("accumulatedCashback", new StringSchema().example("0.005"))
                .addProperty("accumulatedPoints", new StringSchema().example("61.00"))
                .addProperty("expiryPoint", new StringSchema().example("0.00"))
                .addProperty("status", new IntegerSchema().example(1));


        Schema<?> fullSchema = new ObjectSchema()
                .addProperty("status", statusSchema)
                .addProperty("response", responseSchema);

        MediaType mediaType = new MediaType()
                .schema(fullSchema);

        Content content = new Content()
                .addMediaType("application/json", mediaType);

        return new ApiResponse()
                .description("Structured static customer profile")
                .content(content);
    }


    public ApiResponse customerNotFoundExample() {
        Schema<?> statusSchema = new ObjectSchema()
                .addProperty("success", new BooleanSchema().example(false))
                .addProperty("code", new StringSchema().example("LOYALTY-404"))
                .addProperty("reasonCode", new StringSchema().example("Customer does not exist or you have entered invalid customer"));

        Schema<?> responseSchema = new ObjectSchema()
                .addProperty("status", statusSchema);
        Content content = new Content()
                .addMediaType("application/json", new MediaType().schema(responseSchema));
        return new ApiResponse().description("Customer not found response")
                .content(content);

    }
}
