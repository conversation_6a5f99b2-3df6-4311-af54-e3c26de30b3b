package jo.capitalbank.ms.exception;


import jo.capitalbank.ms.library.error.code.ErrorCodeEnum;
import jo.capitalbank.ms.library.error.exception.OBBadGatewayException;
import jo.capitalbank.ms.library.error.exception.OBBadRequestException;
import jo.capitalbank.ms.library.error.exception.OBBusinessException;
import jo.capitalbank.ms.library.error.exception.SubException;

import java.util.List;
import java.util.Map;


public class LoyaltySystemException extends OBBusinessException {

    public LoyaltySystemException(String message, ErrorCodes errorCode,List<SubException> subErrors, Map<String, String> additionalInformation) {
        super(message, errorCode, subErrors, additionalInformation);
    }
    public LoyaltySystemException(List<SubException> subErrors, Map<String, String> additionalInformation) {
        super("Loyalty System Error", ErrorCodes.LOYALTY_SYSTEM_ERROR, subErrors, additionalInformation);
    }

    @Override
    public ErrorCodes getErrorCode() {
        return (ErrorCodes) super.getErrorCode();
    }
}
