package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.CustomerProfileResponse;
import jo.capitalbank.ms.models.responses.CustomerProfileBackendResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface CustomerProfileMapper {

    @Mapping(source = "identifierValue", target = "customerId")
    @Mapping(source = "rjMsg", target = "rjMessage")
    CustomerProfileResponse mapToDto(CustomerProfileBackendResponse.CustomerBodyResponse source);
}
