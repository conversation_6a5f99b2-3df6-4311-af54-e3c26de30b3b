package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.TransactionsResponse;
import jo.capitalbank.ms.models.responses.TransactionsBackendResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TransactionResponseMapper {


    @Mapping(source = "data",target = "transactions")

    TransactionsResponse mapTransactionResponse(TransactionsBackendResponse.TransactionsBackendBody source);


    @Mapping(source = "items", target = "transaction")
    TransactionsResponse.Transaction mapTransactionBody(TransactionsBackendResponse.TransactionBody source);

    TransactionsResponse.TransactionItem mapItem(TransactionsBackendResponse.Items source);
}
